openapi: 3.0.3
info:
  title: 用户个人中心管理API - 更新版
  description: |
    用户个人中心管理相关接口，包括：
    - 获取个人信息
    - 更新个人信息（新增字段）
    - 修改密码
    - 更换邮箱
    
    **更新内容：**
    - 新增性别、入职时间、主教科目、教学经验、个人简介等字段
    - 手机号和邮箱在编辑时设为只读
    - 移除头像更换功能，使用统一默认头像
  version: 2.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境

paths:
  /user/profile:
    get:
      summary: 获取当前用户信息
      description: 获取当前登录用户的详细信息，包括新增的个人资料字段
      tags:
        - 用户个人中心
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserInfo'
              example:
                code: 200
                message: "操作成功"
                data:
                  userId: 1001
                  username: "zhangsan"
                  realName: "张三"
                  role: "teacher"
                  schoolId: 1
                  schoolName: "示例学校"
                  phone: "13800138000"
                  email: "<EMAIL>"
                  gender: "M"
                  hireDate: "2023-01-15"
                  subject: "数学"
                  experience: 5
                  bio: "专业数学教师，致力于为学生提供优质的教学服务。"
                  lastLoginAt: "2024-01-15T14:20:00Z"
                  status: 1
                timestamp: 1640995200000
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/info:
    put:
      summary: 更新用户信息
      description: |
        更新用户个人信息，包括新增的字段：
        - 性别
        - 入职时间
        - 主教科目（仅教师角色）
        - 教学经验（仅教师角色）
        - 个人简介
        
        注意：手机号和邮箱不能通过此接口修改
      tags:
        - 用户个人中心
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserInfoRequest'
            example:
              realName: "张三"
              gender: "M"
              hireDate: "2023-01-15"
              subject: "数学"
              experience: 5
              bio: "专业数学教师，致力于为学生提供优质的教学服务。"
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 200
                message: "用户信息更新成功"
                data: null
                timestamp: 1640995200000
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/password:
    put:
      summary: 修改密码
      description: 修改当前用户的登录密码
      tags:
        - 用户个人中心
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
            example:
              oldPassword: "oldpass123"
              newPassword: "newpass123"
              confirmPassword: "newpass123"
      responses:
        '200':
          description: 密码修改成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 200
                message: "密码修改成功"
                data: null
                timestamp: 1640995200000
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/email/send-code:
    post:
      summary: 发送邮箱验证码
      description: 向指定邮箱发送验证码，用于更换邮箱
      tags:
        - 用户个人中心
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendEmailCodeRequest'
            example:
              email: "<EMAIL>"
      responses:
        '200':
          description: 验证码发送成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 200
                message: "验证码已发送到您的邮箱"
                data: null
                timestamp: 1640995200000
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /user/email:
    put:
      summary: 更换邮箱
      description: 使用验证码更换用户邮箱地址
      tags:
        - 用户个人中心
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEmailRequest'
            example:
              email: "<EMAIL>"
              verificationCode: "123456"
      responses:
        '200':
          description: 邮箱更换成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 200
                message: "邮箱更换成功"
                data: null
                timestamp: 1640995200000
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            code: 400
            message: "请求参数错误"
            data: null
            timestamp: 1640995200000

    Unauthorized:
      description: 未授权访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            code: 401
            message: "未授权访问"
            data: null
            timestamp: 1640995200000

    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            code: 500
            message: "服务器内部错误"
            data: null
            timestamp: 1640995200000

  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          description: 响应数据
          nullable: true
        timestamp:
          type: integer
          format: int64
          description: 响应时间戳
          example: 1640995200000

    ChangePasswordRequest:
      type: object
      required:
        - oldPassword
        - newPassword
        - confirmPassword
      properties:
        oldPassword:
          type: string
          description: 原密码
          example: "oldpass123"
        newPassword:
          type: string
          description: 新密码
          minLength: 6
          maxLength: 20
          example: "newpass123"
        confirmPassword:
          type: string
          description: 确认密码
          example: "newpass123"

    SendEmailCodeRequest:
      type: object
      required:
        - email
      properties:
        email:
          type: string
          format: email
          description: 邮箱地址
          example: "<EMAIL>"

    UpdateEmailRequest:
      type: object
      required:
        - email
        - verificationCode
      properties:
        email:
          type: string
          format: email
          description: 邮箱地址
          example: "<EMAIL>"
        verificationCode:
          type: string
          pattern: '^\d{6}$'
          description: 6位数字验证码
          example: "123456"

    UpdateUserInfoRequest:
      type: object
      required:
        - realName
      properties:
        realName:
          type: string
          description: 真实姓名
          example: "张三"
        gender:
          type: string
          enum: [M, F]
          description: 性别，M-男，F-女
          example: "M"
        hireDate:
          type: string
          format: date
          description: 入职时间
          example: "2023-01-15"
        subject:
          type: string
          description: 主教科目（教师角色使用）
          example: "数学"
        experience:
          type: integer
          minimum: 0
          maximum: 50
          description: 教学经验年数（教师角色使用）
          example: 5
        bio:
          type: string
          description: 个人简介
          example: "专业数学教师，致力于为学生提供优质的教学服务。"

    UserInfo:
      type: object
      properties:
        userId:
          type: integer
          description: 用户ID
          example: 1001
        username:
          type: string
          description: 用户名
          example: "zhangsan"
        realName:
          type: string
          description: 真实姓名
          example: "张三"
        role:
          type: string
          description: 用户角色
          enum: [super_admin, principal, teacher]
          example: "teacher"
        schoolId:
          type: integer
          description: 学校ID
          example: 1
        schoolName:
          type: string
          description: 学校名称
          example: "示例学校"
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
        email:
          type: string
          description: 邮箱地址
          example: "<EMAIL>"
        gender:
          type: string
          enum: [M, F]
          description: 性别，M-男，F-女
          example: "M"
        hireDate:
          type: string
          format: date
          description: 入职时间
          example: "2023-01-15"
        subject:
          type: string
          description: 主教科目（教师角色使用）
          example: "数学"
        experience:
          type: integer
          description: 教学经验年数（教师角色使用）
          example: 5
        bio:
          type: string
          description: 个人简介
          example: "专业数学教师，致力于为学生提供优质的教学服务。"
        lastLoginAt:
          type: string
          format: date-time
          description: 最后登录时间
          example: "2024-01-15T14:20:00Z"
        status:
          type: integer
          description: 账号状态，1-正常，0-禁用
          example: 1
