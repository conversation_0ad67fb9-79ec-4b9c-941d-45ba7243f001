paths:
  /principal/teach/schedule/batch:
    post:
      tags:
        - 教学管理
      summary: 批量排课
      description: 根据周期自动生成重复课程（支持排除节假日）
      parameters:
        - in: header
          name: Authorization
          required: true
          schema:
            type: string
          description: JWT认证token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                teacherId:
                  type: integer
                  example: 1025
                studentId:
                  type: integer
                  example: 3058
                classroomId:
                  type: integer
                  example: 12
                startDate:
                  type: string
                  format: date
                  example: "2025-09-01"
                endDate:
                  type: string
                  format: date
                  example: "2025-12-31"
                repeatDays:
                  type: array
                  items:
                    type: string
                    enum: [MON, TUE, WED, THU, FRI, SAT, SUN]
                  example: ["MON", "WED", "FRI"]
                startTime:
                  type: string
                  format: time
                  example: "14:30:00"
                endTime:
                  type: string
                  format: time
                  example: "16:00:00"
                price:
                  type: number
                  format: float
                  example: 150.00
                gradeLevel:
                  type: string
                  example: "高一"
                excludeHolidays:
                  type: boolean
                  default: true
      responses:
        '200':
          description: 排课成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  conflictCount:
                    type: integer
                    example: 2
                  successCount:
                    type: integer
                    example: 36
                  conflicts:
                    type: array
                    items:
                      type: object
                      properties:
                        date:
                          type: string
                          example: "2025-10-01"
                        reason:
                          type: string
                          example: "节假日冲突"
        '409':
          description: 存在资源冲突
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "该教室在2025-09-03 14:30已被占用"

  /principal/teach/schedule/{courseId}:
    put:
      tags:
        - 教学管理
      summary: 调整单节课程
      description: 拖拽式修改课程时间/教室（自动通知相关老师）
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: integer
          example: 8805
        - in: header
          name: Authorization
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                newDate:
                  type: string
                  format: date
                  example: "2025-09-05"
                newStartTime:
                  type: string
                  format: time
                  example: "15:00:00"
                newEndTime:
                  type: string
                  format: time
                  example: "16:30:00"
                newClassroomId:
                  type: integer
                  example: 15
                notifyTeacher:
                  type: boolean
                  default: true
      responses:
        '200':
          description: 调整成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  notificationId:
                    type: integer
                    example: 33258
        '400':
          description: 新时间冲突
          
  /principal/teach/classrooms:
    get:
      tags:
        - 教学管理
      summary: 教室状态查询
      description: 实时教室使用状态看板
      parameters:
        - in: query
          name: date
          required: true
          schema:
            type: string
            format: date
          example: "2025-09-01"
        - in: header
          name: Authorization
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 教室状态数据
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    classroomId:
                      type: integer
                      example: 12
                    name:
                      type: string
                      example: "物理实验室"
                    status:
                      type: string
                      enum: [available, in_use, maintenance]
                      example: "in_use"
                    currentCourse:
                      type: object
                      properties:
                        courseId:
                          type: integer
                          example: 8805
                        teacherName:
                          type: string
                          example: "张老师"
                        studentName:
                          type: string
                          example: "李明"
                        timeRange:
                          type: string
                          example: "14:30-16:00"
                    utilizationRate:
                      type: number
                      format: float
                      example: 75.3

  /principal/teach/classes:
    post:
      tags:
        - 教学管理
      summary: 创建班级
      parameters:
        - in: header
          name: Authorization
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                className:
                  type: string
                  example: "高一(3)班"
                mainTeacherId:
                  type: integer
                  example: 1025
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  classId:
                    type: integer
                    example: 28
                  
    get:
      tags:
        - 教学管理
      summary: 班级列表查询
      parameters:
        - in: header
          name: Authorization
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 班级数据
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    classId:
                      type: integer
                      example: 28
                    className:
                      type: string
                      example: "高一(3)班"
                    mainTeacher:
                      type: string
                      example: "张老师"
                    studentCount:
                      type: integer
                      example: 45
                    teacherCount:
                      type: integer
                      example: 8

  /principal/teach/classes/{classId}:
    put:
      tags:
        - 教学管理
      summary: 修改班级信息
      parameters:
        - in: path
          name: classId
          required: true
          schema:
            type: integer
          example: 28
        - in: header
          name: Authorization
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                newClassName:
                  type: string
                  example: "高一(3)班-火箭班"
                newMainTeacherId:
                  type: integer
                  example: 1030
      responses:
        '200':
          description: 修改成功
          
    delete:
      tags:
        - 教学管理
      summary: 删除班级
      parameters:
        - in: path
          name: classId
          required: true
          schema:
            type: integer
          example: 28
        - in: header
          name: Authorization
          required: true
          schema:
            type: string
      responses:
        '204':
          description: 删除成功