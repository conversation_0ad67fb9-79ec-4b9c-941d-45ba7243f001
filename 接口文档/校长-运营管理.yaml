paths:
  # 约课管理接口
  /api/operation/bookings:
    get:
      tags: [校长运营管理-约课中心]
      summary: 获取约课请求列表
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, confirmed, rejected]
          description: 约课状态过滤
        - name: startDate
          in: query
          schema:
            type: string
            format: date
          description: 约课开始日期
        - name: endDate
          in: query
          schema:
            type: string
            format: date
          description: 约课结束日期
      responses:
        '200':
          description: 成功获取约课列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: integer
                    example: 15
                  bookings:
                    type: array
                    items:
                      $ref: '#/components/schemas/BookingInfo'
    
    post:
      tags: [校长运营管理-约课中心]
      summary: 处理约课请求
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HandleBookingRequest'
      responses:
        '200':
          description: 处理成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "约课处理成功"

  /api/operation/bookings/conflicts:
    get:
      tags: [校长运营管理-约课中心]
      summary: 获取约课冲突解决方案
      parameters:
        - name: bookingId
          in: query
          required: true
          schema:
            type: integer
          description: 约课ID
      responses:
        '200':
          description: 冲突解决方案列表
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConflictSolution'

  # 消息管理接口
  /api/operation/messages:
    get:
      tags: [校长运营管理-消息中枢]
      summary: 获取消息列表
      parameters:
        - name: type
          in: query
          schema:
            type: string
            enum: [all, booking, notification, payment]
          description: 消息类型过滤
        - name: priority
          in: query
          schema:
            type: string
            enum: [all, normal, urgent]
          description: 优先级过滤
      responses:
        '200':
          description: 成功获取消息列表
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MessageInfo'

  /api/operation/messages/{messageId}/status:
    patch:
      tags: [校长运营管理-消息中枢]
      summary: 更新消息状态
      parameters:
        - name: messageId
          in: path
          required: true
          schema:
            type: integer
          description: 消息ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum: [read, confirmed]
              required: [status]
      responses:
        '200':
          description: 状态更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "消息状态更新成功"

  /api/operation/messages/urgent:
    post:
      tags: [校长运营管理-消息中枢]
      summary: 发送紧急通知
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UrgentMessageRequest'
      responses:
        '200':
          description: 发送成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "紧急通知发送成功"

components:
  schemas:
    # 约课信息模型
    BookingInfo:
      type: object
      properties:
        bookingId:
          type: integer
          example: 1001
        studentId:
          type: integer
          example: 501
        studentName:
          type: string
          example: "张三"
        requestDate:
          type: string
          format: date-time
          example: "2023-10-15T14:30:00Z"
        courseType:
          type: string
          example: "数学辅导"
        status:
          type: string
          enum: [pending, confirmed, rejected]
          example: "pending"
        conflictFlag:
          type: boolean
          example: true
    
    # 处理约课请求模型
    HandleBookingRequest:
      type: object
      properties:
        bookingId:
          type: integer
          example: 1001
        action:
          type: string
          enum: [confirm, reject]
          example: "confirm"
        adjustedTime:
          type: string
          format: date-time
          example: "2023-10-16T15:00:00Z"
        note:
          type: string
          example: "时间调整到次日下午"
      required:
        - bookingId
        - action
    
    # 冲突解决方案模型
    ConflictSolution:
      type: object
      properties:
        solutionId:
          type: integer
          example: 1
        description:
          type: string
          example: "调整至次日下午3点"
        teacherId:
          type: integer
          example: 201
        teacherName:
          type: string
          example: "李老师"
        classroomId:
          type: integer
          example: 301
        classroomName:
          type: string
          example: "301教室"
        adjustedTime:
          type: string
          format: date-time
          example: "2023-10-16T15:00:00Z"
    
    # 消息信息模型
    MessageInfo:
      type: object
      properties:
        messageId:
          type: integer
          example: 7001
        type:
          type: string
          enum: [booking, notification, payment]
          example: "booking"
        title:
          type: string
          example: "新的约课请求"
        content:
          type: string
          example: "学生张三请求10月15日数学辅导"
        sender:
          type: string
          example: "系统"
        priority:
          type: string
          enum: [normal, urgent]
          example: "normal"
        status:
          type: string
          enum: [unread, read, confirmed]
          example: "unread"
        createTime:
          type: string
          format: date-time
          example: "2023-10-10T09:15:00Z"
    
    # 紧急消息请求模型
    UrgentMessageRequest:
      type: object
      properties:
        receiverType:
          type: string
          enum: [teacher, all, class]
          example: "teacher"
        receiverId:
          type: integer
          example: 201
        title:
          type: string
          example: "紧急会议通知"
        content:
          type: string
          example: "今天下午4点所有老师到会议室开会"
      required:
        - receiverType
        - content