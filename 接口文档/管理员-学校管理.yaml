paths:
  /admin/schools:
    get:
      tags: [学校管理]
      summary: 获取学校列表
      parameters:
        - name: status
          in: query
          description: 学校状态过滤
          schema:
            type: string
            enum: [active, inactive]
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 学校列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: integer
                  schools:
                    type: array
                    items:
                      $ref: '#/components/schemas/SchoolInfo'
    post:
      tags: [学校管理]
      summary: 创建学校
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSchoolRequest'
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SchoolInfo'

  /admin/schools/{schoolId}:
    put:
      tags: [学校管理]
      summary: 编辑学校信息
      parameters:
        - name: schoolId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSchoolRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SchoolInfo'
    patch:
      tags: [学校管理]
      summary: 设置学校状态
      parameters:
        - name: schoolId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum: [active, inactive]
      responses:
        '200':
          description: 状态更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  schoolId:
                    type: integer
                  status:
                    type: string

components:
  schemas:
    SchoolInfo:
      type: object
      properties:
        school_id:
          type: integer
          example: 1001
        name:
          type: string
          example: "希望小学"
        address:
          type: string
          example: "北京市海淀区"
        admin_quota:
          type: integer
          example: 3
        status:
          type: string
          example: "active"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateSchoolRequest:
      type: object
      required: [name]
      properties:
        name:
          type: string
          example: "新建学校"
        address:
          type: string
          example: "上海市浦东新区"
        admin_quota:
          type: integer
          example: 2
        status:
          type: string
          enum: [active, inactive]
          default: active

    UpdateSchoolRequest:
      type: object
      properties:
        name:
          type: string
          example: "更新后的校名"
        address:
          type: string
          example: "广州市天河区"
        admin_quota:
          type: integer
          example: 5