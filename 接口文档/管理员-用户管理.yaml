paths:
  /admin/users:
    get:
      tags: [管理员模块-用户管理]
      summary: 查询用户列表
      description: |
        ### 权限要求
        - 仅超级管理员可用  
        ### 功能说明
        支持按角色/学校/姓名等多条件筛选用户
      parameters:
        - name: role
          in: query
          description: 用户角色(super_admin/principal/teacher)
          schema:
            type: string
        - name: schoolId
          in: query
          description: 学校ID
          schema:
            type: integer
        - name: realName
          in: query
          description: 真实姓名(模糊匹配)
          schema:
            type: string
        - name: page
          in: query
          description: 页码(默认1)
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          description: 每页数量(默认10)
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 用户列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: integer
                    example: 35
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/UserInfo'

    post:
      tags: [管理员模块-用户管理]
      summary: 创建用户
      description: |
        ### 权限要求
        - 仅超级管理员可用
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  userId:
                    type: integer
                    example: 10001

  /admin/users/{userId}:
    get:
      tags: [管理员模块-用户管理]
      summary: 获取用户详情
      description: |
        ### 权限要求
        - 仅超级管理员可用
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 用户详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetail'

    put:
      tags: [管理员模块-用户管理]
      summary: 更新用户信息
      description: |
        ### 权限要求
        - 仅超级管理员可用
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: 更新成功

    delete:
      tags: [管理员模块-用户管理]
      summary: 删除用户
      description: |
        ### 权限要求
        - 仅超级管理员可用  
        ### 约束条件
        - 存在关联数据(如排课记录)时不可删除
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 删除成功

  /admin/users/batch-import:
    post:
      tags: [管理员模块-用户管理]
      summary: 批量导入用户
      description: |
        ### 权限要求
        - 仅超级管理员可用  
        ### 文件格式
        - 支持Excel格式
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: 导入结果
          content:
            application/json:
              schema:
                type: object
                properties:
                  successCount:
                    type: integer
                    example: 23
                  failureList:
                    type: array
                    items:
                      type: string
                    example: ["行号3: 用户名重复", "行号7: 学校ID不存在"]

  /admin/users/{userId}/reset-password:
    put:
      tags: [管理员模块-用户管理]
      summary: 重置用户密码
      description: |
        ### 权限要求
        - 仅超级管理员可用
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 重置成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  newPassword:
                    type: string
                    example: "Tmp@123456"

  /admin/users/{userId}/unbind-mfa:
    put:
      tags: [管理员模块-用户管理]
      summary: 解绑MFA设备
      description: |
        ### 权限要求
        - 仅超级管理员可用
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 解绑成功

components:
  schemas:
    UserInfo:
      type: object
      properties:
        userId:
          type: integer
          example: 10001
        username:
          type: string
          example: "zhangsan"
        realName:
          type: string
          example: "张三"
        role:
          type: string
          example: "teacher"
        schoolId:
          type: integer
          example: 201
        schoolName:
          type: string
          example: "育才中学"
        phone:
          type: string
          example: "13800138000"
        createdAt:
          type: string
          format: date-time
          example: "2023-05-10T08:30:00Z"

    UserDetail:
      allOf:
        - $ref: '#/components/schemas/UserInfo'
        - type: object
          properties:
            lastLoginAt:
              type: string
              format: date-time
              example: "2024-01-15T14:20:00Z"
            mfaBound:
              type: boolean
              example: true

    CreateUserRequest:
      type: object
      required: [username, realName, role, schoolId]
      properties:
        username:
          type: string
          example: "lisi_edu"
        password:
          type: string
          example: "Init@123"
        realName:
          type: string
          example: "李四"
        role:
          type: string
          enum: [super_admin, principal, teacher]
          example: "teacher"
        schoolId:
          type: integer
          example: 201
        phone:
          type: string
          example: "13900139000"

    UpdateUserRequest:
      type: object
      properties:
        realName:
          type: string
          example: "李四(修改)"
        phone:
          type: string
          example: "13900139222"
        schoolId:
          type: integer
          example: 202
        status:
          type: string
          enum: [active, inactive]
          example: "active"