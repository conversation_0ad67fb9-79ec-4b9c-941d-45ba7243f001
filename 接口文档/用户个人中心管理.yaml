openapi: 3.0.0
info:
  title: 助教排课系统 - 用户个人中心管理API
  description: 用户个人中心相关功能的API接口文档，包括修改密码、更换邮箱、更新个人信息等
  version: 1.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 本地开发环境
  - url: https://api.teachingassistant.com
    description: 生产环境

paths:
  /user/password:
    put:
      tags:
        - 用户个人中心
      summary: 修改密码
      description: 用户修改登录密码
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
            example:
              oldPassword: "oldpass123"
              newPassword: "newpass123"
              confirmPassword: "newpass123"
      responses:
        '200':
          description: 密码修改成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 200
                message: "密码修改成功"
                data: null
                timestamp: 1640995200000
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 400
                message: "原密码错误"
                data: null
                timestamp: 1640995200000
        '401':
          description: 未授权
        '500':
          description: 服务器内部错误

  /user/email/send-code:
    post:
      tags:
        - 用户个人中心
      summary: 发送邮箱验证码
      description: 发送邮箱验证码用于邮箱绑定或更换
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendEmailCodeRequest'
            example:
              email: "<EMAIL>"
      responses:
        '200':
          description: 验证码发送成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 200
                message: "验证码已发送到您的邮箱"
                data: null
                timestamp: 1640995200000
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 400
                message: "邮箱已被使用"
                data: null
                timestamp: 1640995200000
        '401':
          description: 未授权
        '500':
          description: 服务器内部错误

  /user/email:
    put:
      tags:
        - 用户个人中心
      summary: 更换邮箱
      description: 用户更换绑定的邮箱地址，需要验证码验证
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEmailRequest'
            example:
              email: "<EMAIL>"
              verificationCode: "123456"
      responses:
        '200':
          description: 邮箱更换成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 200
                message: "邮箱更换成功"
                data: null
                timestamp: 1640995200000
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 400
                message: "验证码错误或已过期"
                data: null
                timestamp: 1640995200000
        '401':
          description: 未授权
        '500':
          description: 服务器内部错误



  /user/info:
    put:
      tags:
        - 用户个人中心
      summary: 更新用户信息
      description: 更新用户的基本信息，包括真实姓名
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserInfoRequest'
            example:
              realName: "张三"
      responses:
        '200':
          description: 用户信息更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 200
                message: "用户信息更新成功"
                data: null
                timestamp: 1640995200000
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '500':
          description: 服务器内部错误

  /user/profile:
    get:
      tags:
        - 用户个人中心
      summary: 获取当前用户信息
      description: 获取当前登录用户的详细信息
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 获取用户信息成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserInfo'
              example:
                code: 200
                message: "获取成功"
                data:
                  userId: 1001
                  username: "zhangsan"
                  realName: "张三"
                  role: "teacher"
                  schoolId: 1
                  schoolName: "示例学校"
                  phone: "13800138000"
                  email: "<EMAIL>"
                timestamp: 1640995200000
        '401':
          description: 未授权
        '500':
          description: 服务器内部错误

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          description: 响应数据
          nullable: true
        timestamp:
          type: integer
          format: int64
          description: 响应时间戳
          example: 1640995200000

    ChangePasswordRequest:
      type: object
      required:
        - oldPassword
        - newPassword
        - confirmPassword
      properties:
        oldPassword:
          type: string
          description: 原密码
          example: "oldpass123"
        newPassword:
          type: string
          description: 新密码
          minLength: 6
          maxLength: 20
          example: "newpass123"
        confirmPassword:
          type: string
          description: 确认密码
          example: "newpass123"

    SendEmailCodeRequest:
      type: object
      required:
        - email
      properties:
        email:
          type: string
          format: email
          description: 邮箱地址
          example: "<EMAIL>"

    UpdateEmailRequest:
      type: object
      required:
        - email
        - verificationCode
      properties:
        email:
          type: string
          format: email
          description: 邮箱地址
          example: "<EMAIL>"
        verificationCode:
          type: string
          pattern: '^\d{6}$'
          description: 6位数字验证码
          example: "123456"

    UpdateUserInfoRequest:
      type: object
      required:
        - realName
      properties:
        realName:
          type: string
          description: 真实姓名
          example: "张三"

    UserInfo:
      type: object
      properties:
        userId:
          type: integer
          description: 用户ID
          example: 1001
        username:
          type: string
          description: 用户名
          example: "zhangsan"
        realName:
          type: string
          description: 真实姓名
          example: "张三"
        role:
          type: string
          description: 用户角色
          enum: [super_admin, principal, teacher]
          example: "teacher"
        schoolId:
          type: integer
          description: 学校ID
          example: 1
        schoolName:
          type: string
          description: 学校名称
          example: "示例学校"
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
        email:
          type: string
          description: 邮箱地址
          example: "<EMAIL>"
