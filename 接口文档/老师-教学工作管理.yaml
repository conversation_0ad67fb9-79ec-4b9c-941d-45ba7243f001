# 老师角色 - 教学工作管理模块接口文档
paths:
  /teacher/courses/schedule:
    get:
      summary: 获取老师课程表
      description: 支持日/周/月视图切换，课前1小时自动推送提醒
      tags: [教师-教学工作]
      parameters:
        - name: viewType
          in: query
          description: 视图类型(day/week/month)
          required: true
          schema:
            type: string
            enum: [day, week, month]
        - name: date
          in: query
          description: 查询日期(格式yyyy-MM-dd)
          required: true
          schema:
            type: string
            format: date
      responses:
        '200':
          description: 成功获取课程表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/CourseSchedule'
        '401':
          description: 未授权访问

  /teacher/attendance:
    post:
      summary: 记录学生考勤
      description: 人脸识别快速签到，缺勤自动通知家长
      tags: [教师-教学工作]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/AttendanceRecord'
      responses:
        '200':
          description: 考勤记录成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: 考勤记录已保存
        '400':
          description: 数据校验失败

  /teacher/attendance/history:
    get:
      summary: 查询考勤历史
      description: 按时间范围查询考勤记录
      tags: [教师-教学工作]
      parameters:
        - name: startDate
          in: query
          description: 开始日期(格式yyyy-MM-dd)
          required: true
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: 结束日期(格式yyyy-MM-dd)
          required: true
          schema:
            type: string
            format: date
      responses:
        '200':
          description: 成功获取考勤记录
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/AttendanceHistory'

components:
  schemas:
    CourseSchedule:
      type: object
      properties:
        courseId:
          type: integer
          example: 1001
          description: 课程ID
        courseDate:
          type: string
          format: date
          example: "2023-05-15"
          description: 上课日期
        startTime:
          type: string
          example: "14:00:00"
          description: 开始时间
        endTime:
          type: string
          example: "15:30:00"
          description: 结束时间
        classroom:
          type: string
          example: "302教室"
          description: 教室名称
        studentName:
          type: string
          example: "张三"
          description: 学生姓名
        gradeLevel:
          type: string
          example: "五年级"
          description: 年级
        courseContent:
          type: string
          example: "数学-几何基础知识"
          description: 课程内容
        reminderSent:
          type: boolean
          example: true
          description: 是否已发送提醒

    AttendanceRecord:
      type: object
      required: [courseId, studentId, status]
      properties:
        courseId:
          type: integer
          example: 1001
          description: 课程ID
        studentId:
          type: integer
          example: 2001
          description: 学生ID
        status:
          type: string
          enum: [present, absent, late]
          example: present
          description: 出勤状态
        lateMinutes:
          type: integer
          example: 5
          description: 迟到分钟数(仅当status=late时有效)
        note:
          type: string
          example: "学生身体不适提前离开"
          description: 备注信息

    AttendanceHistory:
      type: object
      properties:
        attendanceId:
          type: integer
          example: 3001
          description: 考勤记录ID
        courseDate:
          type: string
          format: date
          example: "2023-05-15"
        startTime:
          type: string
          example: "14:00:00"
        studentName:
          type: string
          example: "张三"
        status:
          type: string
          example: "present"
        recordedAt:
          type: string
          format: date-time
          example: "2023-05-15T14:05:00Z"
        note:
          type: string
          example: "学生认真听讲"