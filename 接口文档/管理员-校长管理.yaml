openapi: 3.0.0
info:
  title: 助教排课系统 - 校长管理接口
  version: 1.0.0
  description: 管理员角色核心功能模块 - 校长管理接口文档

paths:
  /api/admin/principals:
    get:
      tags: [校长管理]
      summary: 获取校长列表
      description: 分页查询系统所有校长信息
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 20
        - name: schoolId
          in: query
          description: 学校ID
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: 成功获取校长列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 25
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/PrincipalInfo'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    
    post:
      tags: [校长管理]
      summary: 创建校长账号
      description: 为指定学校创建新的校长账号
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePrincipalRequest'
      responses:
        '201':
          description: 校长账号创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 201
                  data:
                    type: object
                    properties:
                      userId:
                        type: integer
                        example: 1024
                      username:
                        type: string
                        example: principal_zhang
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          description: 用户名已存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  
  /api/admin/principals/batch-import:
    post:
      tags: [校长管理]
      summary: 批量导入校长账号
      description: 通过Excel文件批量导入校长账号
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Excel文件(支持xlsx格式)
      responses:
        '200':
          description: 批量导入成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: object
                    properties:
                      successCount:
                        type: integer
                        example: 15
                      failureCount:
                        type: integer
                        example: 2
                      failureReasons:
                        type: array
                        items:
                          type: string
                        example: ["第3行: 学校ID不存在", "第7行: 用户名已存在"]
        '400':
          $ref: '#/components/responses/BadRequest'
        '415':
          description: 不支持的文件类型
  
  /api/admin/principals/{userId}/reset-password:
    put:
      tags: [校长管理]
      summary: 重置校长密码
      description: 重置指定校长的登录密码
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 密码重置成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: string
                    example: "新密码已发送至绑定手机"
        '404':
          $ref: '#/components/responses/NotFound'
        '422':
          description: 未绑定手机无法重置
  
  /api/admin/principals/{userId}/unbind-mfa:
    put:
      tags: [校长管理]
      summary: 解绑MFA设备
      description: 解除校长账号的多因素认证设备绑定
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: MFA设备解绑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: string
                    example: "MFA设备已解绑"
        '404':
          $ref: '#/components/responses/NotFound'
  
  /api/admin/principals/{userId}:
    put:
      tags: [校长管理]
      summary: 更新校长信息
      description: 更新校长基本信息
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                realName:
                  type: string
                  example: "张校长"
                phone:
                  type: string
                  example: "13800138000"
                schoolId:
                  type: integer
                  example: 1001
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    $ref: '#/components/schemas/PrincipalInfo'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
    
    delete:
      tags: [校长管理]
      summary: 删除校长账号
      description: 删除指定校长账号（逻辑删除）
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: 删除成功
        '400':
          description: 存在关联数据无法删除
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 400
                  message:
                    type: string
                    example: "该校长管理的学生数据未转移，无法删除"
        '404':
          $ref: '#/components/responses/NotFound'

components:
  schemas:
    PrincipalInfo:
      type: object
      properties:
        userId:
          type: integer
          example: 1001
        username:
          type: string
          example: "principal_zhang"
        realName:
          type: string
          example: "张校长"
        phone:
          type: string
          example: "13800138000"
        schoolId:
          type: integer
          example: 2001
        schoolName:
          type: string
          example: "第一中学"
        mfaBound:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time
          example: "2023-06-15T10:30:00Z"
    
    CreatePrincipalRequest:
      type: object
      required:
        - username
        - realName
        - schoolId
        - phone
      properties:
        username:
          type: string
          example: "new_principal"
        password:
          type: string
          example: "Init@123"
        realName:
          type: string
          example: "李校长"
        schoolId:
          type: integer
          example: 2001
        phone:
          type: string
          example: "13900139000"
    
    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          example: 400
        message:
          type: string
          example: "请求参数无效"
        errors:
          type: array
          items:
            type: string
          example: ["学校ID不能为空"]
  
  responses:
    Unauthorized:
      description: 未认证或凭证无效
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                example: 401
              message:
                type: string
                example: "身份认证失败"
    
    Forbidden:
      description: 权限不足
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                example: 403
              message:
                type: string
                example: "没有操作权限"
    
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    
    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                example: 404
              message:
                type: string
                example: "校长账号不存在"