openapi: 3.0.0
info:
  title: 助教排课系统 - 基础模块API
  version: 1.0.0
  description: 系统基础功能接口文档（登录、权限、个人中心等）

paths:
  /api/login:
    post:
      tags:
        - 认证模块
      summary: 用户登录
      description: 账号密码登录，系统自动识别角色
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: 用户名或密码错误
        '403':
          description: 账户已禁用

  /api/logout:
    post:
      tags:
        - 认证模块
      summary: 用户登出
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true

  /api/user/profile:
    get:
      tags:
        - 个人中心
      summary: 获取当前用户信息
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取用户信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'

  /api/user/password:
    put:
      tags:
        - 个人中心
      summary: 修改密码
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePasswordRequest'
      responses:
        '200':
          description: 密码修改成功
        '401':
          description: 原始密码错误

  /api/user/phone:
    put:
      tags:
        - 个人中心
      summary: 绑定手机号
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                phone:
                  type: string
                  example: "13800138000"
              required:
                - phone
      responses:
        '200':
          description: 手机绑定成功

  /api/user/settings:
    get:
      tags:
        - 个人中心
      summary: 获取用户设置
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取设置
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSettings'

    put:
      tags:
        - 个人中心
      summary: 更新用户设置
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserSettings'
      responses:
        '200':
          description: 设置更新成功

  /api/user/logs:
    get:
      tags:
        - 个人中心
      summary: 获取用户操作日志
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功获取日志
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: integer
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/OperationLog'

  /api/menu:
    get:
      tags:
        - 权限管理
      summary: 获取动态菜单
      description: 根据用户角色返回权限菜单
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取菜单
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MenuItem'

components:
  schemas:
    LoginRequest:
      type: object
      properties:
        username:
          type: string
          example: "admin"
        password:
          type: string
          example: "password123"
        captcha:
          type: string
          example: "ABCDE"
      required:
        - username
        - password

    LoginResponse:
      type: object
      properties:
        userId:
          type: integer
          example: 1
        username:
          type: string
          example: "admin"
        realName:
          type: string
          example: "管理员"
        role:
          type: string
          enum: [super_admin, principal, teacher]
          example: "super_admin"
        schoolId:
          type: integer
          example: 1
        token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

    UpdatePasswordRequest:
      type: object
      properties:
        oldPassword:
          type: string
          example: "oldPassword"
        newPassword:
          type: string
          example: "newPassword"
      required:
        - oldPassword
        - newPassword

    UserProfile:
      type: object
      properties:
        userId:
          type: integer
          example: 1
        username:
          type: string
          example: "admin"
        realName:
          type: string
          example: "管理员"
        role:
          type: string
          enum: [super_admin, principal, teacher]
          example: "super_admin"
        phone:
          type: string
          example: "13800138000"
        schoolId:
          type: integer
          example: 1
        schoolName:
          type: string
          example: "示范学校"

    UserSettings:
      type: object
      properties:
        notifyCourse:
          type: boolean
          example: true
          description: 课程提醒
        notifyPayment:
          type: boolean
          example: true
          description: 缴费提醒
        notifySystem:
          type: boolean
          example: true
          description: 系统通知
        theme:
          type: string
          enum: [light, dark]
          example: "light"
          description: 主题设置

    OperationLog:
      type: object
      properties:
        logId:
          type: integer
          example: 1
        operation:
          type: string
          example: "用户登录"
        ipAddress:
          type: string
          example: "***********"
        result:
          type: string
          enum: [success, failure]
          example: "success"
        details:
          type: string
          example: "登录成功"
        createdAt:
          type: string
          format: date-time
          example: "2023-08-01T10:30:00Z"

    MenuItem:
      type: object
      properties:
        name:
          type: string
          example: "教学管理"
        path:
          type: string
          example: "/teaching"
        icon:
          type: string
          example: "el-icon-notebook-2"
        children:
          type: array
          items:
            $ref: '#/components/schemas/SubMenuItem'

    SubMenuItem:
      type: object
      properties:
        name:
          type: string
          example: "排课管理"
        path:
          type: string
          example: "/teaching/schedule"
        permission:
          type: string
          example: "teaching:schedule"

securitySchemes:
  bearerAuth:
    type: http
    scheme: bearer
    bearerFormat: JWT