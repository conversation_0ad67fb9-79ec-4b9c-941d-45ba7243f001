swagger: "2.0"
info:
  title: 助教系统 - 老师端学生管理模块
  version: 1.0.0
  description: |
    ### 数据权限说明
    - 老师仅能访问自己关联的学生数据
    - 学生信息通过teacher_student关联表实现权限隔离

paths:
  /api/teacher/students:
    get:
      tags: ["学生管理"]
      summary: 获取负责的学生列表
      description: 分页获取当前老师负责的学生列表（包含基础信息）
      parameters:
        - name: page
          in: query
          type: integer
          default: 1
          description: 页码
        - name: pageSize
          in: query
          type: integer
          default: 10
          description: 每页数量
        - name: className
          in: query
          type: string
          required: false
          description: 班级名称筛选
      responses:
        200:
          description: 成功获取学生列表
          schema:
            type: object
            properties:
              total:
                type: integer
                example: 25
              items:
                type: array
                items:
                  $ref: "#/definitions/StudentBasicInfo"
        401:
          description: 未授权访问
        403:
          description: 禁止访问其他学校数据

  /api/teacher/students/{studentId}:
    get:
      tags: ["学生管理"]
      summary: 获取学生详细信息
      description: 获取单个学生的完整档案（含出勤统计和学习进度）
      parameters:
        - name: studentId
          in: path
          type: integer
          required: true
          description: 学生ID
      responses:
        200:
          description: 成功获取学生详情
          schema:
            $ref: "#/definitions/StudentDetailInfo"
        404:
          description: 学生不存在或未关联
        403:
          description: 禁止访问其他学校数据

  /api/teacher/students/{studentId}/attendance:
    get:
      tags: ["学生管理"]
      summary: 获取学生出勤记录
      description: 分页获取指定学生的历史出勤记录
      parameters:
        - name: studentId
          in: path
          type: integer
          required: true
          description: 学生ID
        - name: startDate
          in: query
          type: string
          format: date
          description: 筛选开始日期 (YYYY-MM-DD)
        - name: endDate
          in: query
          type: string
          format: date
          description: 筛选结束日期 (YYYY-MM-DD)
        - name: status
          in: query
          type: string
          enum: [present, absent, late]
          description: 出勤状态筛选
      responses:
        200:
          description: 成功获取出勤记录
          schema:
            type: object
            properties:
              studentId:
                type: integer
                example: 1001
              attendanceStats:
                $ref: "#/definitions/AttendanceStats"
              records:
                type: array
                items:
                  $ref: "#/definitions/AttendanceRecord"
        403:
          description: 禁止访问其他学生数据

  /api/teacher/students/{studentId}/contact:
    get:
      tags: ["学生管理"]
      summary: 获取学生联系方式
      description: 获取学生及家长的联系方式（含CTI集成信息）
      parameters:
        - name: studentId
          in: path
          type: integer
          required: true
          description: 学生ID
      responses:
        200:
          description: 成功获取联系方式
          schema:
            $ref: "#/definitions/ContactInfo"
        403:
          description: 禁止访问其他学生数据

  /api/teacher/students/search:
    get:
      tags: ["学生管理"]
      summary: 搜索学生
      description: 按姓名/班级快速搜索负责的学生
      parameters:
        - name: keyword
          in: query
          type: string
          required: true
          description: 姓名或班级关键词
      responses:
        200:
          description: 成功获取搜索结果
          schema:
            type: array
            items:
              $ref: "#/definitions/StudentSearchResult"
        403:
          description: 禁止跨学校搜索

definitions:
  StudentBasicInfo:
    type: object
    properties:
      studentId:
        type: integer
        example: 1001
      name:
        type: string
        example: "张三"
      className:
        type: string
        example: "初二(3)班"
      contactPhone:
        type: string
        example: "13800138000"
      attendanceRate:
        type: number
        format: float
        example: 92.5
        description: 本月出勤率

  StudentDetailInfo:
    type: object
    properties:
      basicInfo:
        $ref: "#/definitions/StudentBasicInfo"
      learningProgress:
        type: object
        properties:
          subjects:
            type: array
            items:
              type: object
              properties:
                subject:
                  type: string
                  example: "数学"
                progress:
                  type: number
                  format: float
                  example: 85.0
          radarChart:
            type: array
            items:
              $ref: "#/definitions/RadarDataPoint"
        description: 学习进度雷达图数据
      lastUpdate:
        type: string
        format: date-time
        example: "2025-07-28T10:30:00Z"

  RadarDataPoint:
    type: object
    properties:
      dimension:
        type: string
        example: "代数"
      score:
        type: number
        format: float
        example: 92

  AttendanceStats:
    type: object
    properties:
      totalClasses:
        type: integer
        example: 20
      present:
        type: integer
        example: 18
      absent:
        type: integer
        example: 1
      late:
        type: integer
        example: 1
      rate:
        type: number
        format: float
        example: 95.0

  AttendanceRecord:
    type: object
    properties:
      courseDate:
        type: string
        format: date
        example: "2025-07-20"
      courseTime:
        type: string
        example: "14:00-15:30"
      subject:
        type: string
        example: "物理"
      status:
        type: string
        enum: [present, absent, late]
        example: "present"
      recordedAt:
        type: string
        format: date-time
        example: "2025-07-20T15:35:00Z"

  ContactInfo:
    type: object
    properties:
      studentPhone:
        type: string
        example: "13800138000"
      parentPhone:
        type: string
        example: "13900139000"
      ctiEnabled:
        type: boolean
        example: true
        description: 是否支持CTI一键拨打
      lastContactTime:
        type: string
        format: date-time
        example: "2025-07-15T16:20:00Z"

  StudentSearchResult:
    type: object
    properties:
      studentId:
        type: integer
        example: 1002
      name:
        type: string
        example: "李四"
      className:
        type: string
        example: "初三(1)班"
      contactPhone:
        type: string
        example: "13700137000"
      latestCourse:
        type: string
        example: "2025-07-29 15:00 英语"