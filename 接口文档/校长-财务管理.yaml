swagger: "2.0"
info:
  version: 1.0.0
  title: 助教系统-校长角色财务管理模块
  description: 校长角色的财务管理相关接口

paths:
  # 老师工资查询接口
  /api/principal/salaries:
    get:
      tags:
        - 财务管理
      summary: 获取老师工资明细列表
      description: 分页查询本校所有老师的工资明细（数据隔离到当前校长所属学校）
      parameters:
        - name: page
          in: query
          type: integer
          required: false
          default: 1
          description: 页码
        - name: pageSize
          in: query
          type: integer
          required: false
          default: 10
          description: 每页数量
        - name: teacherName
          in: query
          type: string
          required: false
          description: 老师姓名模糊搜索
        - name: period
          in: query
          type: string
          format: date
          required: false
          description: 薪资周期（格式 YYYY-MM）
      responses:
        '200':
          description: 成功获取工资列表
          schema:
            $ref: '#/definitions/SalaryPageResult'
        '403':
          description: 无权限操作

  # 学生未付款查询接口
  /api/principal/students/unpaid:
    get:
      tags:
        - 财务管理
      summary: 获取未付款学生列表
      description: 分页查询本校未付款学生信息（数据隔离到当前校长所属学校）
      parameters:
        - name: minAmount
          in: query
          type: number
          format: decimal
          required: false
          description: 最小未付金额
        - name: maxAmount
          in: query
          type: number
          format: decimal
          required: false
          description: 最大未付金额
        - name: classId
          in: query
          type: integer
          required: false
          description: 按班级筛选
      responses:
        '200':
          description: 成功获取学生列表
          schema:
            $ref: '#/definitions/UnpaidStudentList'
        '403':
          description: 无权限操作

  # 学生缴费记录查询
  /api/principal/students/{studentId}/payments:
    get:
      tags:
        - 财务管理
      summary: 获取学生缴费记录
      description: 查询指定学生的历史缴费记录（按学年分组）
      parameters:
        - name: studentId
          in: path
          type: integer
          required: true
          description: 学生ID
        - name: schoolYear
          in: query
          type: string
          required: false
          description: 筛选学年（格式 2023-2024）
      responses:
        '200':
          description: 成功获取缴费记录
          schema:
            $ref: '#/definitions/StudentPaymentHistory'
        '404':
          description: 学生不存在

  # 未付款学生导出
  /api/principal/students/unpaid/export:
    post:
      tags:
        - 财务管理
      summary: 导出未付款学生列表
      description: 导出CSV格式的未付款学生清单
      parameters:
        - name: exportRequest
          in: body
          required: true
          schema:
            $ref: '#/definitions/UnpaidExportRequest'
      responses:
        '200':
          description: 导出成功
          schema:
            type: file
        '403':
          description: 无权限操作

definitions:
  # 分页响应结构
  SalaryPageResult:
    type: object
    properties:
      total:
        type: integer
        example: 100
      data:
        type: array
        items:
          $ref: '#/definitions/TeacherSalaryDTO'

  # 老师工资DTO
  TeacherSalaryDTO:
    type: object
    properties:
      salaryId:
        type: integer
        example: 1001
      teacherId:
        type: integer
        example: 2001
      teacherName:
        type: string
        example: "张老师"
      period:
        type: string
        format: date
        example: "2023-10"
      baseAmount:
        type: number
        format: decimal
        example: 8000.00
      bonus:
        type: number
        format: decimal
        example: 1200.50
      deductions:
        type: number
        format: decimal
        example: 300.00
      totalAmount:
        type: number
        format: decimal
        example: 8900.50
      courseHours:
        type: integer
        example: 40

  # 未付款学生列表
  UnpaidStudentList:
    type: array
    items:
      $ref: '#/definitions/UnpaidStudentDTO'

  # 未付款学生DTO
  UnpaidStudentDTO:
    type: object
    properties:
      studentId:
        type: integer
        example: 3001
      studentName:
        type: string
        example: "李明"
      className:
        type: string
        example: "三年二班"
      contactPhone:
        type: string
        example: "13800138000"
      unpaidAmount:
        type: number
        format: decimal
        example: 2500.00
      lastPaymentDate:
        type: string
        format: date
        example: "2023-09-15"

  # 学生缴费历史
  StudentPaymentHistory:
    type: object
    properties:
      studentId:
        type: integer
        example: 3001
      studentName:
        type: string
        example: "李明"
      paymentByYear:
        type: array
        items:
          $ref: '#/definitions/PaymentYearSummary'

  # 年度缴费摘要
  PaymentYearSummary:
    type: object
    properties:
      schoolYear:
        type: string
        example: "2023-2024"
      totalAmount:
        type: number
        format: decimal
        example: 6000.00
      paymentDetails:
        type: array
        items:
          $ref: '#/definitions/PaymentRecordDTO'

  # 缴费记录DTO
  PaymentRecordDTO:
    type: object
    properties:
      paymentId:
        type: integer
        example: 5001
      amount:
        type: number
        format: decimal
        example: 2000.00
      paymentDate:
        type: string
        format: date
        example: "2023-09-15"
      createdByName:
        type: string
        example: "财务处"

  # 导出请求参数
  UnpaidExportRequest:
    type: object
    properties:
      minAmount:
        type: number
        format: decimal
        example: 100.00
      maxAmount:
        type: number
        format: decimal
        example: 5000.00
      classIds:
        type: array
        items:
          type: integer
        example: [101, 102]