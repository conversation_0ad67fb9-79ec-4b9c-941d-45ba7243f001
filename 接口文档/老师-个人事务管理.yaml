openapi: 3.0.0
info:
  title: 助教系统老师角色核心功能API
  version: 1.0.0
  description: 老师角色个人事务管理模块接口文档

paths:
  # ========================
  # 工资查询接口
  # ========================
  /api/teacher/salaries:
    get:
      tags:
        - 工资查询
      summary: 获取工资列表
      description: 查询当前登录老师的工资列表（按月汇总）
      parameters:
        - name: year
          in: query
          description: 筛选年份
          schema:
            type: integer
            example: 2023
        - name: month
          in: query
          description: 筛选月份
          schema:
            type: integer
            example: 8
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功获取工资列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: integer
                    example: 5
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/SalarySummary'

  /api/teacher/salaries/{salaryId}:
    get:
      tags:
        - 工资查询
      summary: 获取工资单详情
      description: 查看指定工资单的详细构成
      parameters:
        - name: salaryId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功获取工资详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalaryDetail'

  # ========================
  # 消息管理接口
  # ========================
  /api/teacher/messages:
    get:
      tags:
        - 消息管理
      summary: 获取消息列表
      description: 分页查询当前老师的消息（支持状态/类型/优先级过滤）
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [unread, read, confirmed]
            default: unread
        - name: type
          in: query
          schema:
            type: string
            enum: [booking, notification, payment]
        - name: priority
          in: query
          schema:
            type: string
            enum: [normal, urgent]
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功获取消息列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: integer
                    example: 15
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/MessageSummary'

  /api/teacher/messages/{messageId}:
    get:
      tags:
        - 消息管理
      summary: 获取消息详情
      description: 查看消息详细信息
      parameters:
        - name: messageId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功获取消息详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageDetail'

    patch:
      tags:
        - 消息管理
      summary: 更新消息状态
      description: 标记消息为已读或已确认（校长通知需确认）
      parameters:
        - name: messageId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action:
                  type: string
                  enum: [mark_as_read, confirm]
                  example: mark_as_read
              required:
                - action
      responses:
        '200':
          description: 状态更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: 消息状态已更新

  # ========================
  # 个人信息接口
  # ========================
  /api/teacher/profile:
    get:
      tags:
        - 个人信息
      summary: 获取教师档案
      description: 获取当前登录教师的基本信息
      responses:
        '200':
          description: 成功获取教师档案
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeacherProfile'

    put:
      tags:
        - 个人信息
      summary: 更新教师档案
      description: 修改个人信息（手机号/密码等）
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TeacherProfileUpdate'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: 个人信息更新成功

components:
  schemas:
    # ========================
    # 工资数据结构
    # ========================
    SalarySummary:
      type: object
      properties:
        salaryId:
          type: integer
          example: 1001
        period:
          type: string
          format: date
          example: "2023-08-01"
        totalAmount:
          type: number
          format: float
          example: 8650.00
        status:
          type: string
          enum: [pending, paid]
          example: paid

    SalaryDetail:
      type: object
      properties:
        salaryId:
          type: integer
          example: 1001
        period:
          type: string
          example: "2023年8月"
        baseAmount:
          type: number
          example: 8000.00
        bonus:
          type: number
          example: 1000.00
        deductions:
          type: number
          example: 350.00
        totalAmount:
          type: number
          example: 8650.00
        paymentDate:
          type: string
          format: date
          example: "2023-09-05"
        breakdown:
          type: array
          items:
            type: object
            properties:
              courseDate:
                type: string
                format: date
              studentName:
                type: string
              duration:
                type: number
              amount:
                type: number

    # ========================
    # 消息数据结构
    # ========================
    MessageSummary:
      type: object
      properties:
        messageId:
          type: integer
          example: 2005
        title:
          type: string
          example: "新课程安排通知"
        senderType:
          type: string
          enum: [system, principal, parent]
          example: principal
        senderName:
          type: string
          example: "张校长"
        messageType:
          type: string
          enum: [booking, notification, payment]
          example: notification
        priority:
          type: string
          enum: [normal, urgent]
          example: urgent
        status:
          type: string
          enum: [unread, read, confirmed]
          example: unread
        createdAt:
          type: string
          format: date-time
          example: "2023-08-15T14:30:00Z"

    MessageDetail:
      type: object
      properties:
        messageId:
          type: integer
          example: 2005
        title:
          type: string
          example: "新课程安排通知"
        content:
          type: string
          example: "您下周新增周三下午3点的数学辅导课程，学生：李小明"
        sender:
          type: object
          properties:
            userId:
              type: integer
            name:
              type: string
            role:
              type: string
        attachments:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              url:
                type: string
        requiresConfirm:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time

    # ========================
    # 教师档案数据结构
    # ========================
    TeacherProfile:
      type: object
      properties:
        userId:
          type: integer
          example: 50001
        realName:
          type: string
          example: "王老师"
        phone:
          type: string
          example: "13800138000"
        teachingSubject:
          type: string
          example: "数学"
        totalStudents:
          type: integer
          example: 15
        monthlyCourses:
          type: integer
          example: 40

    TeacherProfileUpdate:
      type: object
      properties:
        phone:
          type: string
          example: "13800138888"
        password:
          type: string
          example: "newPassword123"
        notificationPreference:
          type: object
          properties:
            sms:
              type: boolean
            appPush:
              type: boolean
            email:
              type: boolean