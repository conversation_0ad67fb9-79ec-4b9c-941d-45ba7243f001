openapi: 3.0.3
info:
  title: 助教排课系统 - 系统基础模块（验证码更新版）
  description: |
    助教排课系统的系统基础模块接口文档，包含用户认证、验证码等核心功能。
    
    **更新内容：**
    - 新增验证码生成接口
    - 登录接口增加验证码校验参数
    - 增强安全性验证流程
    
    **验证码功能说明：**
    - 验证码为4位字母数字组合
    - 图片尺寸：120×40像素
    - 有效期：5分钟
    - 验证成功后立即失效
    - 支持点击刷新
  version: 2.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api
    description: 本地开发环境
  - url: https://api.teachingassistant.com
    description: 生产环境

tags:
  - name: 认证管理
    description: 用户登录、登出、Token管理等认证相关接口
  - name: 验证码管理
    description: 图形验证码生成和验证相关接口

paths:
  /captcha/generate:
    get:
      tags:
        - 验证码管理
      summary: 生成验证码
      description: |
        生成图形验证码，返回验证码图片的Base64编码和对应的验证码密钥。
        
        **功能特点：**
        - 生成4位字母数字组合验证码
        - 图片尺寸：120×40像素
        - 包含干扰线和噪点
        - 验证码有效期5分钟
        - 每次调用生成新的验证码
        
        **使用场景：**
        - 用户访问登录页面时自动调用
        - 用户点击验证码图片刷新时调用
        - 登录失败后重新获取验证码
      operationId: generateCaptcha
      responses:
        '200':
          description: 验证码生成成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/CaptchaResponse'
              examples:
                success:
                  summary: 成功示例
                  value:
                    code: 200
                    message: "验证码生成成功"
                    data:
                      captchaKey: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
                      captchaImage: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAoCAYAAAA..."
                    timestamp: 1704067200000
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                error:
                  summary: 错误示例
                  value:
                    code: 500
                    message: "生成验证码失败"
                    data: null
                    timestamp: 1704067200000

  /login:
    post:
      tags:
        - 认证管理
      summary: 用户登录（增强版）
      description: |
        用户登录接口，支持验证码校验。登录成功后返回JWT Token和用户信息。
        
        **验证流程：**
        1. 验证码存在性检查（是否过期）
        2. 验证码文本匹配检查（不区分大小写）
        3. 传统账号密码校验
        4. 验证成功后立即删除验证码记录
        
        **安全特性：**
        - 验证码一次性使用
        - 登录失败后需重新获取验证码
        - 支持防暴力破解
        - JWT Token有效期24小时
      operationId: loginWithCaptcha
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
            examples:
              admin_login:
                summary: 管理员登录
                value:
                  username: "admin"
                  password: "admin123"
                  captcha: "A1B2"
                  captchaKey: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
              principal_login:
                summary: 校长登录
                value:
                  username: "principal001"
                  password: "admin123"
                  captcha: "X9Y8"
                  captchaKey: "b2c3d4e5-f6g7-8901-bcde-f23456789012"
              teacher_login:
                summary: 老师登录
                value:
                  username: "teacher001"
                  password: "admin123"
                  captcha: "M5N6"
                  captchaKey: "c3d4e5f6-g7h8-9012-cdef-345678901234"
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/LoginResponse'
              examples:
                admin_success:
                  summary: 管理员登录成功
                  value:
                    code: 200
                    message: "登录成功"
                    data:
                      accessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                      tokenType: "Bearer"
                      expiresIn: 86400
                      userInfo:
                        userId: 1
                        username: "admin"
                        realName: "系统管理员"
                        role: "super_admin"
                        schoolId: null
                        schoolName: null
                        phone: "13800138000"
                        email: "<EMAIL>"
                        status: 1
                    timestamp: 1704067200000
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                missing_captcha:
                  summary: 缺少验证码
                  value:
                    code: 400
                    message: "请输入验证码"
                    data: null
                    timestamp: 1704067200000
                invalid_captcha:
                  summary: 验证码错误
                  value:
                    code: 400
                    message: "验证码错误"
                    data: null
                    timestamp: 1704067200000
                expired_captcha:
                  summary: 验证码过期
                  value:
                    code: 400
                    message: "验证码已过期或不存在"
                    data: null
                    timestamp: 1704067200000
        '401':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                login_failed:
                  summary: 用户名或密码错误
                  value:
                    code: 401
                    message: "用户名或密码错误"
                    data: null
                    timestamp: 1704067200000

  /logout:
    post:
      tags:
        - 认证管理
      summary: 用户登出
      description: 用户登出，清除服务端会话信息
      operationId: logout
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                success:
                  summary: 登出成功
                  value:
                    code: 200
                    message: "登出成功"
                    data: null
                    timestamp: 1704067200000

  /user/profile:
    get:
      tags:
        - 认证管理
      summary: 获取当前用户信息
      description: 获取当前登录用户的详细信息
      operationId: getCurrentUser
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserInfo'
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /refresh-token:
    post:
      tags:
        - 认证管理
      summary: 刷新Token
      description: 刷新JWT Token，延长登录有效期
      operationId: refreshToken
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Token刷新成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Token无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT Token认证，格式：Bearer {token}

  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          description: 响应数据
          nullable: true
        timestamp:
          type: integer
          format: int64
          description: 响应时间戳
          example: 1704067200000
      required:
        - code
        - message
        - timestamp

    CaptchaResponse:
      type: object
      properties:
        captchaKey:
          type: string
          description: 验证码密钥，用于验证时标识验证码
          example: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
        captchaImage:
          type: string
          description: 验证码图片的Base64编码，包含data:image/png;base64,前缀
          example: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAoCAYAAAA..."
      required:
        - captchaKey
        - captchaImage

    LoginRequest:
      type: object
      properties:
        username:
          type: string
          description: 用户名
          minLength: 3
          maxLength: 20
          example: "admin"
        password:
          type: string
          description: 密码
          minLength: 6
          maxLength: 20
          example: "admin123"
        captcha:
          type: string
          description: 验证码文本（4位字符，不区分大小写）
          minLength: 4
          maxLength: 4
          example: "A1B2"
        captchaKey:
          type: string
          description: 验证码密钥，从验证码生成接口获取
          example: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
      required:
        - username
        - password
        - captcha
        - captchaKey

    LoginResponse:
      type: object
      properties:
        accessToken:
          type: string
          description: JWT访问令牌
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        tokenType:
          type: string
          description: 令牌类型
          example: "Bearer"
        expiresIn:
          type: integer
          description: 令牌有效期（秒）
          example: 86400
        userInfo:
          $ref: '#/components/schemas/UserInfo'
      required:
        - accessToken
        - tokenType
        - expiresIn
        - userInfo

    UserInfo:
      type: object
      properties:
        userId:
          type: integer
          description: 用户ID
          example: 1
        username:
          type: string
          description: 用户名
          example: "admin"
        realName:
          type: string
          description: 真实姓名
          example: "系统管理员"
        role:
          type: string
          enum: [super_admin, principal, teacher]
          description: 用户角色
          example: "super_admin"
        schoolId:
          type: integer
          description: 学校ID（超级管理员为null）
          nullable: true
          example: 1
        schoolName:
          type: string
          description: 学校名称
          nullable: true
          example: "示例学校"
        phone:
          type: string
          description: 手机号码
          nullable: true
          example: "13800138000"
        email:
          type: string
          description: 邮箱地址
          nullable: true
          example: "<EMAIL>"
        gender:
          type: string
          enum: [M, F]
          description: 性别
          nullable: true
          example: "M"
        hireDate:
          type: string
          format: date
          description: 入职日期
          nullable: true
          example: "2024-01-01"
        subject:
          type: string
          description: 任教科目
          nullable: true
          example: "数学"
        experience:
          type: integer
          description: 工作经验（年）
          nullable: true
          example: 5
        bio:
          type: string
          description: 个人简介
          nullable: true
          example: "资深教师，擅长数学教学"
        lastLoginAt:
          type: string
          format: date-time
          description: 最后登录时间
          nullable: true
          example: "2024-01-01T10:00:00"
        status:
          type: integer
          description: 用户状态（1-激活，0-禁用）
          example: 1
      required:
        - userId
        - username
        - realName
        - role
        - status
