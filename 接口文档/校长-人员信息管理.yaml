swagger: '2.0'
info:
  title: 助教排课系统 - 校长端接口文档
  version: 1.0.0
  description: 校长角色核心功能模块 - 人员信息管理接口

paths:
  # 1. 老师查询接口
  /api/principal/teachers:
    get:
      tags: [人员信息管理]
      summary: 查询所有老师信息
      description: 分页查询本校所有老师信息，支持姓名搜索
      parameters:
        - name: page
          in: query
          type: integer
          default: 1
          description: 页码
        - name: size
          in: query
          type: integer
          default: 10
          description: 每页数量
        - name: name
          in: query
          type: string
          required: false
          description: 老师姓名(模糊查询)
      responses:
        200:
          description: 成功获取老师列表
          schema:
            type: object
            properties:
              code:
                type: integer
                example: 200
              message:
                type: string
                example: "success"
              data:
                type: object
                properties:
                  total:
                    type: integer
                    example: 25
                  teachers:
                    type: array
                    items:
                      $ref: '#/definitions/TeacherBasicInfo'
        401:
          description: 未授权访问

  # 2. 老师详情接口
  /api/principal/teachers/{teacherId}:
    get:
      tags: [人员信息管理]
      summary: 获取老师详细信息
      description: 获取老师详情（含关联学生及工资详情）
      parameters:
        - name: teacherId
          in: path
          type: integer
          required: true
          description: 老师ID
      responses:
        200:
          description: 成功获取老师详情
          schema:
            type: object
            properties:
              code:
                type: integer
                example: 200
              message:
                type: string
                example: "success"
              data:
                $ref: '#/definitions/TeacherDetailInfo'
        404:
          description: 老师不存在

  # 3. 学生查询接口
  /api/principal/students:
    get:
      tags: [人员信息管理]
      summary: 查询所有学生信息
      description: 分页查询本校所有学生信息，支持姓名/班级搜索
      parameters:
        - name: page
          in: query
          type: integer
          default: 1
          description: 页码
        - name: size
          in: query
          type: integer
          default: 10
          description: 每页数量
        - name: name
          in: query
          type: string
          required: false
          description: 学生姓名(模糊查询)
        - name: classId
          in: query
          type: integer
          required: false
          description: 班级ID
      responses:
        200:
          description: 成功获取学生列表
          schema:
            type: object
            properties:
              code:
                type: integer
                example: 200
              message:
                type: string
                example: "success"
              data:
                type: object
                properties:
                  total:
                    type: integer
                    example: 120
                  students:
                    type: array
                    items:
                      $ref: '#/definitions/StudentBasicInfo'
        401:
          description: 未授权访问

  # 4. 学生详情接口
  /api/principal/students/{studentId}:
    get:
      tags: [人员信息管理]
      summary: 获取学生详细信息
      description: 获取学生基本信息及未付款金额
      parameters:
        - name: studentId
          in: path
          type: integer
          required: true
          description: 学生ID
      responses:
        200:
          description: 成功获取学生详情
          schema:
            type: object
            properties:
              code:
                type: integer
                example: 200
              message:
                type: string
                example: "success"
              data:
                $ref: '#/definitions/StudentDetailInfo'
        404:
          description: 学生不存在

definitions:
  # 老师基本信息
  TeacherBasicInfo:
    type: object
    properties:
      teacherId:
        type: integer
        example: 1001
        description: 老师ID
      name:
        type: string
        example: "张老师"
        description: 老师姓名
      phone:
        type: string
        example: "13800138000"
        description: 联系电话
      courseCount:
        type: integer
        example: 12
        description: 本周课程数
      studentCount:
        type: integer
        example: 8
        description: 关联学生数

  # 老师详情信息
  TeacherDetailInfo:
    type: object
    properties:
      teacherId:
        type: integer
        example: 1001
      realName:
        type: string
        example: "张三"
      phone:
        type: string
        example: "13800138000"
      students:
        type: array
        description: 关联学生列表
        items:
          $ref: '#/definitions/TeacherStudentInfo'
      salaryInfo:
        $ref: '#/definitions/TeacherSalaryInfo'

  # 老师关联学生信息
  TeacherStudentInfo:
    type: object
    properties:
      studentId:
        type: integer
        example: 2001
      name:
        type: string
        example: "李明"
      className:
        type: string
        example: "三年级二班"
      lastAttend:
        type: string
        enum: [present, absent, late]
        example: "present"
        description: 最近考勤状态

  # 老师工资信息
  TeacherSalaryInfo:
    type: object
    properties:
      period:
        type: string
        format: date
        example: "2024-06"
        description: 薪资周期
      baseAmount:
        type: number
        format: double
        example: 4500.00
      bonus:
        type: number
        format: double
        example: 800.00
      totalAmount:
        type: number
        format: double
        example: 5300.00

  # 学生基本信息
  StudentBasicInfo:
    type: object
    properties:
      studentId:
        type: integer
        example: 2001
      name:
        type: string
        example: "李明"
      className:
        type: string
        example: "三年级二班"
      contactPhone:
        type: string
        example: "13900139000"
      unpaidAmount:
        type: number
        format: double
        example: 1200.50
        description: 未付金额

  # 学生详情信息
  StudentDetailInfo:
    type: object
    properties:
      studentId:
        type: integer
        example: 2001
      name:
        type: string
        example: "李明"
      classId:
        type: integer
        example: 3001
      className:
        type: string
        example: "三年级二班"
      contactPhone:
        type: string
        example: "13900139000"
      unpaidAmount:
        type: number
        format: double
        example: 1200.50
      lastPaymentDate:
        type: string
        format: date
        example: "2024-06-15"
        description: 最近缴费日期
      mainTeacher:
        type: string
        example: "张老师"