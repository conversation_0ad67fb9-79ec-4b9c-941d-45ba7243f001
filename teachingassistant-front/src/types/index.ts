// 通用响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 分页结果类型
export interface PageResult<T = any> {
  page: number
  size: number
  total: number
  pages: number
  records: T[]
  hasNext: boolean
  hasPrevious: boolean
}

// 用户相关类型
export interface User {
  userId: number
  username: string
  realName: string
  role: 'super_admin' | 'principal' | 'teacher'
  schoolId?: number
  phone?: string
  email?: string
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
  school?: School
}

export interface UserInfo {
  userId: number
  username: string
  realName: string
  role: string
  schoolId?: number
  schoolName?: string
  phone?: string
  email?: string
  gender?: 'M' | 'F'
  avatar?: string
  hireDate?: string
  subject?: string
  experience?: number
  lastLoginAt?: string
  status?: number
  bio?: string
}

// 登录相关类型
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
  captchaKey?: string
}

export interface LoginResponse {
  accessToken: string
  tokenType: string
  expiresIn: number
  userInfo: UserInfo
}

// 学校相关类型
export interface School {
  schoolId: number
  name: string
  address?: string
  adminQuota: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

// 学生相关类型
export interface Student {
  studentId: number
  schoolId: number
  name: string
  classId?: number
  contactPhone?: string
  unpaidAmount: number
  createdAt: string
  updatedAt: string
  schoolClass?: SchoolClass
  school?: School
}

// 班级相关类型
export interface SchoolClass {
  classId: number
  schoolId: number
  name: string
  mainTeacher?: number
  createdAt: string
  updatedAt: string
  mainTeacherInfo?: User
  school?: School
  studentCount?: number
  teacherCount?: number
}

// 教室相关类型
export interface Classroom {
  classroomId: number
  schoolId: number
  name: string
  status: 'available' | 'in_use' | 'maintenance'
  createdAt: string
  updatedAt: string
  school?: School
  currentCourse?: Course
  utilizationRate?: number
}

// 课程相关类型
export interface Course {
  courseId: number
  schoolId: number
  teacherId: number
  studentId: number
  classroomId: number
  courseDate: string
  startTime: string
  endTime: string
  price: number
  gradeLevel: string
  status: 'scheduled' | 'completed' | 'cancelled'
  createdAt: string
  updatedAt: string
  teacher?: User
  student?: Student
  classroom?: Classroom
  school?: School
}

// 消息相关类型
export interface Message {
  messageId: number
  schoolId: number
  senderId?: number
  receiverId: number
  type: 'booking' | 'notification' | 'payment'
  content: string
  status: 'unread' | 'read' | 'confirmed'
  priority: 'normal' | 'urgent'
  createdAt: string
}

// 约课相关类型
export interface Booking {
  bookingId: number
  schoolId: number
  studentId: number
  teacherId?: number
  classroomId?: number
  preferredDate: string
  preferredTime: string
  duration: number
  gradeLevel: string
  subject?: string
  status: 'pending' | 'confirmed' | 'rejected'
  remarks?: string
  processedBy?: number
  processedAt?: string
  createdAt: string
}

// 工资相关类型
export interface Salary {
  salaryId: number
  teacherId: number
  period: string
  baseAmount: number
  bonus: number
  deductions: number
  totalAmount: number
  createdAt: string
}

// 菜单相关类型
export interface MenuItem {
  id: string
  title: string
  icon?: string
  path?: string
  children?: MenuItem[]
  roles?: string[]
  hidden?: boolean
}

// 路由元信息类型
export interface RouteMeta {
  title?: string
  icon?: string
  roles?: string[]
  hidden?: boolean
  keepAlive?: boolean
  breadcrumb?: boolean
}

// 表格列配置类型
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | string
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  type?: 'selection' | 'index' | 'expand'
}

// 表单项配置类型
export interface FormItem {
  prop: string
  label: string
  type: 'input' | 'select' | 'date' | 'datetime' | 'time' | 'number' | 'textarea' | 'switch' | 'radio' | 'checkbox'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  rules?: any[]
  span?: number
  disabled?: boolean
  readonly?: boolean
}

// 查询参数类型
export interface QueryParams {
  page?: number
  size?: number
  [key: string]: any
}
