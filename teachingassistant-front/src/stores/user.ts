import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'
import type { UserInfo, LoginRequest, LoginResponse } from '@/types'
import { request } from '@/utils/request'

const TOKEN_KEY = 'teaching_assistant_token'
const USER_INFO_KEY = 'teaching_assistant_user_info'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(Cookies.get(TOKEN_KEY) || '')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isAdmin = computed(() => userInfo.value?.role === 'super_admin')
  const isPrincipal = computed(() => userInfo.value?.role === 'principal')
  const isTeacher = computed(() => userInfo.value?.role === 'teacher')
  const userName = computed(() => userInfo.value?.realName || userInfo.value?.username || '')
  const schoolName = computed(() => userInfo.value?.schoolName || '')
  
  // 初始化用户信息
  const initUserInfo = () => {
    const savedUserInfo = localStorage.getItem(USER_INFO_KEY)
    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem(USER_INFO_KEY)
      }
    }
  }
  
  // 登录
  const login = async (loginData: LoginRequest): Promise<LoginResponse> => {
    try {
      const response = await request.post<LoginResponse>('/login', loginData)
      const { accessToken, userInfo: user } = response.data
      
      // 保存token和用户信息
      setToken(accessToken)
      setUserInfo(user)
      
      return response.data
    } catch (error) {
      throw error
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      await request.post('/logout')
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地数据
      clearUserData()
    }
  }
  
  // 获取用户信息
  const getUserInfo = async (): Promise<UserInfo> => {
    try {
      const response = await request.get<UserInfo>('/user/profile')
      setUserInfo(response.data)
      return response.data
    } catch (error) {
      throw error
    }
  }
  
  // 刷新Token
  const refreshToken = async (): Promise<string> => {
    try {
      const response = await request.post<LoginResponse>('/refresh-token')
      const { accessToken, userInfo: user } = response.data
      
      setToken(accessToken)
      setUserInfo(user)
      
      return accessToken
    } catch (error) {
      // 刷新失败，清除用户数据
      clearUserData()
      throw error
    }
  }
  
  // 设置Token
  const setToken = (newToken: string) => {
    token.value = newToken
    Cookies.set(TOKEN_KEY, newToken, { expires: 7 }) // 7天过期
  }
  
  // 设置用户信息
  const setUserInfo = (user: UserInfo) => {
    userInfo.value = user
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(user))
  }
  
  // 清除用户数据
  const clearUserData = () => {
    token.value = ''
    userInfo.value = null
    permissions.value = []
    Cookies.remove(TOKEN_KEY)
    localStorage.removeItem(USER_INFO_KEY)
  }
  
  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }
  
  // 检查角色
  const hasRole = (role: string): boolean => {
    return userInfo.value?.role === role
  }
  
  // 检查是否有任一角色
  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some(role => hasRole(role))
  }
  
  // 更新用户信息
  const updateUserInfo = (updates: Partial<UserInfo>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...updates }
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo.value))
    }
  }
  
  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string, confirmPassword: string) => {
    try {
      await request.put('/user/password', {
        oldPassword,
        newPassword,
        confirmPassword
      })
    } catch (error) {
      throw error
    }
  }

  // 发送邮箱验证码
  const sendEmailVerificationCode = async (email: string) => {
    try {
      await request.post('/user/email/send-code', { email })
    } catch (error) {
      throw error
    }
  }

  // 更换邮箱
  const updateEmail = async (email: string, verificationCode: string) => {
    try {
      await request.put('/user/email', { email, verificationCode })
      updateUserInfo({ email })
    } catch (error) {
      throw error
    }
  }

  // 更新用户基本信息
  const updateProfile = async (data: {
    realName: string
    gender?: string
    hireDate?: string
    subject?: string
    experience?: number
    bio?: string
  }) => {
    try {
      await request.put('/user/info', data)
      updateUserInfo(data)
    } catch (error) {
      throw error
    }
  }
  
  // 初始化
  initUserInfo()
  
  return {
    // 状态
    token: readonly(token),
    userInfo: readonly(userInfo),
    permissions: readonly(permissions),
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    isPrincipal,
    isTeacher,
    userName,
    schoolName,
    
    // 方法
    login,
    logout,
    getUserInfo,
    refreshToken,
    setToken,
    setUserInfo,
    clearUserData,
    hasPermission,
    hasRole,
    hasAnyRole,
    updateUserInfo,
    changePassword,
    sendEmailVerificationCode,
    updateEmail,
    updateProfile
  }
})

export default useUserStore
