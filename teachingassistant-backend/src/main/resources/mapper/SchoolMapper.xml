<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teachingassistant.mapper.SchoolMapper">

    <!-- 结果映射 -->
    <resultMap id="SchoolResultMap" type="com.teachingassistant.entity.School">
        <id property="schoolId" column="school_id"/>
        <result property="name" column="name"/>
        <result property="address" column="address"/>
        <result property="adminQuota" column="admin_quota"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        school_id, name, address, admin_quota, status, created_at, updated_at
    </sql>

    <!-- 根据学校ID查询学校 -->
    <select id="findById" resultMap="SchoolResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM schools
        WHERE school_id = #{schoolId}
    </select>

    <!-- 查询所有学校列表 -->
    <select id="findAll" resultMap="SchoolResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM schools
        ORDER BY created_at DESC
    </select>

    <!-- 根据状态查询学校列表 -->
    <select id="findByStatus" resultMap="SchoolResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM schools
        WHERE status = #{status}
        ORDER BY created_at DESC
    </select>

    <!-- 插入学校 -->
    <insert id="insert" parameterType="com.teachingassistant.entity.School" useGeneratedKeys="true" keyProperty="schoolId">
        INSERT INTO schools (name, address, admin_quota, status)
        VALUES (#{name}, #{address}, #{adminQuota}, #{status})
    </insert>

    <!-- 更新学校信息 -->
    <update id="update" parameterType="com.teachingassistant.entity.School">
        UPDATE schools
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="address != null">address = #{address},</if>
            <if test="adminQuota != null">admin_quota = #{adminQuota},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            updated_at = CURRENT_TIMESTAMP
        </set>
        WHERE school_id = #{schoolId}
    </update>

    <!-- 删除学校 -->
    <delete id="deleteById">
        DELETE FROM schools WHERE school_id = #{schoolId}
    </delete>

    <!-- 检查学校名称是否存在 -->
    <select id="existsByName" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM schools WHERE name = #{name}
    </select>

</mapper>
