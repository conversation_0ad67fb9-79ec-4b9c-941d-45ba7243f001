package com.teachingassistant.service.impl;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.entity.User;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.mapper.UserMapper;
import com.teachingassistant.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    
    @Override
    public User findByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户名不能为空");
        }
        return userMapper.findByUsername(username);
    }
    
    @Override
    public User findById(Long userId) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        return user;
    }
    
    @Override
    public List<User> findBySchoolId(Long schoolId) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        return userMapper.findBySchoolId(schoolId);
    }
    
    @Override
    public List<User> findByRole(String role) {
        if (!StringUtils.hasText(role)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "角色不能为空");
        }
        return userMapper.findByRole(role);
    }
    
    @Override
    public List<User> findBySchoolIdAndRole(Long schoolId, String role) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        if (!StringUtils.hasText(role)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "角色不能为空");
        }
        return userMapper.findBySchoolIdAndRole(schoolId, role);
    }
    
    @Override
    public PageResult<User> findWithPagination(Integer page, Integer size, Long schoolId, String role, String realName) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        Integer offset = (page - 1) * size;
        List<User> users = userMapper.findWithPagination(offset, size, schoolId, role, realName);
        Integer total = userMapper.countUsers(schoolId, role, realName);
        
        return PageResult.of(page, size, total.longValue(), users);
    }
    
    @Override
    @Transactional
    public User createUser(User user) {
        validateUserForCreate(user);
        
        // 检查用户名是否已存在
        if (existsByUsername(user.getUsername())) {
            throw new BusinessException(ResultCode.USER_EXISTS, "用户名已存在");
        }
        
        // 检查手机号是否已存在
        if (StringUtils.hasText(user.getPhone()) && existsByPhone(user.getPhone())) {
            throw new BusinessException(ResultCode.USER_EXISTS, "手机号已存在");
        }

        // 检查邮箱是否已存在
        if (StringUtils.hasText(user.getEmail()) && existsByEmail(user.getEmail())) {
            throw new BusinessException(ResultCode.USER_EXISTS, "邮箱已存在");
        }
        
        // 加密密码
        user.setPassword(encodePassword(user.getPassword()));
        
        // 设置默认状态
        if (!StringUtils.hasText(user.getStatus())) {
            user.setStatus("active");
        }
        
        int result = userMapper.insert(user);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "用户创建失败");
        }
        
        log.info("用户创建成功: {}", user.getUsername());
        return findById(user.getUserId());
    }
    
    @Override
    @Transactional
    public User updateUser(User user) {
        if (user.getUserId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        
        // 检查用户是否存在
        User existingUser = findById(user.getUserId());
        
        // 如果更新手机号，检查是否已存在
        if (StringUtils.hasText(user.getPhone()) && !user.getPhone().equals(existingUser.getPhone())) {
            if (existsByPhone(user.getPhone())) {
                throw new BusinessException(ResultCode.USER_EXISTS, "手机号已存在");
            }
        }

        // 如果更新邮箱，检查是否已存在
        if (StringUtils.hasText(user.getEmail()) && !user.getEmail().equals(existingUser.getEmail())) {
            if (existsByEmail(user.getEmail())) {
                throw new BusinessException(ResultCode.USER_EXISTS, "邮箱已存在");
            }
        }
        
        int result = userMapper.update(user);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "用户更新失败");
        }
        
        log.info("用户更新成功: {}", user.getUserId());
        return findById(user.getUserId());
    }
    
    @Override
    @Transactional
    public void updatePassword(Long userId, String oldPassword, String newPassword) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        if (!StringUtils.hasText(oldPassword)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "原密码不能为空");
        }
        if (!StringUtils.hasText(newPassword)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "新密码不能为空");
        }
        
        User user = findById(userId);
        
        // 验证原密码
        if (!validatePassword(oldPassword, user.getPassword())) {
            throw new BusinessException(ResultCode.PASSWORD_ERROR, "原密码错误");
        }
        
        // 更新密码
        String encodedPassword = encodePassword(newPassword);
        int result = userMapper.updatePassword(userId, encodedPassword);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "密码更新失败");
        }
        
        log.info("用户密码更新成功: {}", userId);
    }
    
    @Override
    @Transactional
    public void resetPassword(Long userId, String newPassword) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        if (!StringUtils.hasText(newPassword)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "新密码不能为空");
        }
        
        // 检查用户是否存在
        findById(userId);
        
        // 重置密码
        String encodedPassword = encodePassword(newPassword);
        int result = userMapper.updatePassword(userId, encodedPassword);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "密码重置失败");
        }
        
        log.info("用户密码重置成功: {}", userId);
    }
    
    @Override
    @Transactional
    public void updateStatus(Long userId, String status) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        if (!StringUtils.hasText(status)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "状态不能为空");
        }
        
        // 检查用户是否存在
        findById(userId);
        
        int result = userMapper.updateStatus(userId, status);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "状态更新失败");
        }
        
        log.info("用户状态更新成功: {} -> {}", userId, status);
    }
    
    @Override
    @Transactional
    public void deleteUser(Long userId) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        
        // 检查用户是否存在
        findById(userId);
        
        int result = userMapper.deleteById(userId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "用户删除失败");
        }
        
        log.info("用户删除成功: {}", userId);
    }
    
    @Override
    public boolean existsByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        return userMapper.existsByUsername(username);
    }
    
    @Override
    public boolean existsByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        return userMapper.existsByPhone(phone);
    }

    @Override
    public boolean existsByEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        return userMapper.existsByEmail(email);
    }

    @Override
    @Transactional
    public void updateEmail(Long userId, String email) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        if (!StringUtils.hasText(email)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "邮箱不能为空");
        }

        // 检查用户是否存在
        User existingUser = findById(userId);

        // 检查邮箱是否已存在
        if (!email.equals(existingUser.getEmail()) && existsByEmail(email)) {
            throw new BusinessException(ResultCode.USER_EXISTS, "邮箱已存在");
        }

        // 更新邮箱
        User user = new User();
        user.setUserId(userId);
        user.setEmail(email);

        int result = userMapper.update(user);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "邮箱更新失败");
        }

        log.info("用户邮箱更新成功: {}", userId);
    }
    
    @Override
    public boolean validatePassword(String rawPassword, String encodedPassword) {
        if (!StringUtils.hasText(rawPassword) || !StringUtils.hasText(encodedPassword)) {
            return false;
        }
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
    
    @Override
    public String encodePassword(String rawPassword) {
        if (!StringUtils.hasText(rawPassword)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "密码不能为空");
        }
        return passwordEncoder.encode(rawPassword);
    }

    @Override
    @Transactional
    public void updateLastLoginTime(Long userId) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }

        // 检查用户是否存在
        findById(userId);

        // 更新最后登录时间
        int result = userMapper.updateLastLoginTime(userId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "最后登录时间更新失败");
        }

        log.info("用户最后登录时间更新成功: {}", userId);
    }
    
    /**
     * 验证用户创建参数
     */
    private void validateUserForCreate(User user) {
        if (user == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户信息不能为空");
        }
        if (!StringUtils.hasText(user.getUsername())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户名不能为空");
        }
        if (!StringUtils.hasText(user.getPassword())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "密码不能为空");
        }
        if (!StringUtils.hasText(user.getRole())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "角色不能为空");
        }
        if (!StringUtils.hasText(user.getRealName())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "真实姓名不能为空");
        }
    }
}
