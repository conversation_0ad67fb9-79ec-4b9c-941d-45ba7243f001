package com.teachingassistant.service;

import com.teachingassistant.entity.School;

import java.util.List;

/**
 * 学校服务接口
 * 
 * <AUTHOR> Assistant System
 */
public interface SchoolService {
    
    /**
     * 根据学校ID查询学校
     */
    School findById(Long schoolId);
    
    /**
     * 查询所有学校列表
     */
    List<School> findAll();
    
    /**
     * 根据状态查询学校列表
     */
    List<School> findByStatus(String status);
    
    /**
     * 创建学校
     */
    School createSchool(School school);
    
    /**
     * 更新学校信息
     */
    School updateSchool(School school);
    
    /**
     * 删除学校
     */
    void deleteSchool(Long schoolId);
    
    /**
     * 检查学校名称是否存在
     */
    boolean existsByName(String name);
}
