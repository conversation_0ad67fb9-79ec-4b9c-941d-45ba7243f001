package com.teachingassistant.mapper;

import com.teachingassistant.entity.School;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学校数据访问层
 * 
 * <AUTHOR> Assistant System
 */
@Mapper
public interface SchoolMapper {
    
    /**
     * 根据学校ID查询学校
     */
    School findById(@Param("schoolId") Long schoolId);
    
    /**
     * 查询所有学校列表
     */
    List<School> findAll();
    
    /**
     * 根据状态查询学校列表
     */
    List<School> findByStatus(@Param("status") String status);
    
    /**
     * 插入学校
     */
    int insert(School school);
    
    /**
     * 更新学校信息
     */
    int update(School school);
    
    /**
     * 删除学校
     */
    int deleteById(@Param("schoolId") Long schoolId);
    
    /**
     * 检查学校名称是否存在
     */
    boolean existsByName(@Param("name") String name);
}
