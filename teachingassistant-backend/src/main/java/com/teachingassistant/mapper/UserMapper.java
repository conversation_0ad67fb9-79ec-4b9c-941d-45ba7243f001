package com.teachingassistant.mapper;

import com.teachingassistant.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户数据访问层
 * 
 * <AUTHOR> Assistant System
 */
@Mapper
public interface UserMapper {
    
    /**
     * 根据用户名查询用户
     */
    User findByUsername(@Param("username") String username);
    
    /**
     * 根据用户ID查询用户
     */
    User findById(@Param("userId") Long userId);
    
    /**
     * 根据学校ID查询用户列表
     */
    List<User> findBySchoolId(@Param("schoolId") Long schoolId);
    
    /**
     * 根据角色查询用户列表
     */
    List<User> findByRole(@Param("role") String role);
    
    /**
     * 根据学校ID和角色查询用户列表
     */
    List<User> findBySchoolIdAndRole(@Param("schoolId") Long schoolId, @Param("role") String role);
    
    /**
     * 分页查询用户列表
     */
    List<User> findWithPagination(@Param("offset") Integer offset, 
                                  @Param("limit") Integer limit,
                                  @Param("schoolId") Long schoolId,
                                  @Param("role") String role,
                                  @Param("realName") String realName);
    
    /**
     * 统计用户数量
     */
    Integer countUsers(@Param("schoolId") Long schoolId,
                       @Param("role") String role,
                       @Param("realName") String realName);
    
    /**
     * 插入用户
     */
    int insert(User user);
    
    /**
     * 更新用户信息
     */
    int update(User user);
    
    /**
     * 更新用户密码
     */
    int updatePassword(@Param("userId") Long userId, @Param("password") String password);
    
    /**
     * 更新用户状态
     */
    int updateStatus(@Param("userId") Long userId, @Param("status") String status);
    
    /**
     * 删除用户
     */
    int deleteById(@Param("userId") Long userId);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(@Param("username") String username);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(@Param("phone") String phone);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(@Param("email") String email);

    /**
     * 更新最后登录时间
     */
    int updateLastLoginTime(@Param("userId") Long userId);
}
