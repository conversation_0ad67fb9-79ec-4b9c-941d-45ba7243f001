package com.teachingassistant.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 更新用户信息请求DTO
 *
 * <AUTHOR> Assistant System
 */
@Data
public class UpdateUserInfoRequest {

    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空")
    private String realName;

    /**
     * 性别：M-男，F-女
     */
    private String gender;

    /**
     * 入职时间
     */
    private String hireDate;

    /**
     * 主教科目（教师角色使用）
     */
    private String subject;

    /**
     * 教学经验年数（教师角色使用）
     */
    @Min(value = 0, message = "教学经验不能小于0年")
    @Max(value = 50, message = "教学经验不能超过50年")
    private Integer experience;

    /**
     * 个人简介
     */
    private String bio;
}
